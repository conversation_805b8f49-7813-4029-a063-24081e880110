{"name": "@typescript-eslint/typescript-estree", "version": "8.41.0", "description": "A parser that converts TypeScript source code into an ESTree compatible form", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json", "./use-at-your-own-risk": {"types": "./dist/use-at-your-own-risk.d.ts", "default": "./dist/use-at-your-own-risk.js"}}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/typescript-estree"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/typescript-estree", "license": "MIT", "keywords": ["ast", "estree", "ecmascript", "javascript", "typescript", "parser", "syntax"], "scripts": {"build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/project-service": "8.41.0", "@typescript-eslint/tsconfig-utils": "8.41.0", "@typescript-eslint/types": "8.41.0", "@typescript-eslint/visitor-keys": "8.41.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "devDependencies": {"@types/is-glob": "^4.0.4", "@vitest/coverage-v8": "^3.1.3", "eslint": "*", "glob": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "typescript-estree", "includedScripts": ["clean"], "targets": {"lint": {"command": "eslint"}}}}